from appium.webdriver.webdriver import WebDriver
from appium.webdriver.common.appiumby import AppiumBy
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class BasePage:
    def __init__(self, driver):
        self.driver = driver

    def move_up(self, count=1):
        """Используя keycode указываем сколько раз нужно двигаться вверх"""
        for _ in range(count):
            self.driver.press_keycode(19)

    def move_down(self, count=1):
        """Используя keycode указываем сколько раз нужно двигаться вниз"""
        for _ in range(count):
            self.driver.press_keycode(20)

    def move_left(self, count=1):
        """Используя keycode указываем сколько раз нужно двигаться влево"""
        for _ in range(count):
            self.driver.press_keycode(21)

    def move_right(self, count=1):
        """Используя keycode указываем сколько раз нужно двигаться вправо"""
        for _ in range(count):
            self.driver.press_keycode(22)

    def select(self):
        """Используя keycode нажимаем ОК"""
        self.driver.press_keycode(23)

    def get_text_from_element(self, locator, timeout=50):
        """Получаем текст из элемента с явным ожиданием"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.visibility_of_element_located(locator)
            )
            return element.text
        except (NoSuchElementException, TimeoutException) as e:
            print(f"Элемент не найден: {locator}")
            raise e

    def wait_for_element(self, locator, timeout=50):
        """Ожидаем появление элемента"""
        try:
            WebDriverWait(self.driver, timeout).until(
                EC.visibility_of_element_located(locator)
            )
        except (NoSuchElementException, TimeoutException) as e:
            print(f"Элемент не найден: {locator}")
            raise e
