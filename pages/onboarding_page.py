from appium.webdriver.common.appiumby import AppiumBy
from pages.base_page import BasePage


class OnboardingPageLocators:
    onboarding_step_1 = (AppiumBy.XPATH, '//android.widget.TextView[@text="1 Шаг"]')
    onboarding_step_2 = (AppiumBy.XPATH, '//android.widget.TextView[@text="2 Шаг"]')
    onboarding_step_3 = (AppiumBy.XPATH, '//android.widget.TextView[@text="3 Шаг"]')
    onboarding_step_4 = (AppiumBy.XPATH, '//android.widget.TextView[@text="4 Шаг"]')
    onboarding_step_5 = (AppiumBy.XPATH, '//android.widget.TextView[@text="5 Шаг"]')
    skip_onboarding_button = (
        AppiumBy.XPATH,
        '//android.widget.TextView[@text="Пропустить"]',
    )


class OnboardingPage(BasePage):
    def __init__(self, driver):
        self.driver = driver

    def onboarding_step_1(self):
        self.wait_for_element(OnboardingPageLocators.onboarding_step_1)
        self.move_right(
            2
        )  # 2 раза вправо так как в первый раз закрывается иннформация о том куда нажимать, затем уже листаются шаги
        print("Step 1 passed")
        return True

    def onboarding_step_2(self):
        self.wait_for_element(OnboardingPageLocators.onboarding_step_2)
        self.move_right()
        print("Step 2 passed")
        return True

    def onboarding_step_3(self):
        self.wait_for_element(OnboardingPageLocators.onboarding_step_3)
        self.move_right()
        print("Step 3 passed")
        return True

    def onboarding_step_4(self):
        self.wait_for_element(OnboardingPageLocators.onboarding_step_4)
        self.move_right()
        print("Step 4 passed")
        return True

    def onboarding_step_5(self):
        self.wait_for_element(OnboardingPageLocators.onboarding_step_5)
        self.select()
        print("Step 5 passed")
        return True
