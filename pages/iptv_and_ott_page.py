from appium.webdriver.common.appiumby import AppiumBy
from pages.base_page import BasePage
from test_data import creds

formatted_ott_login = creds.formated_ott_login(creds.ott_login)


class IPTVAndOTTPageLocators:

    skip_onboarding_button = (
        AppiumBy.XPATH,
        '//android.widget.TextView[@text="Пропустить"]',
    )

    main_page_button = (AppiumBy.XPATH, '//android.widget.TextView[@text="Главная"]')

    check_iptv_login = (
        AppiumBy.XPATH,
        f'//android.widget.TextView[@text="{creds.iptv_login}"]',
    )

    check_ott_login = (
        AppiumBy.XPATH,
        f'//android.widget.TextView[@text="{formatted_ott_login}"]',
    )


class IPTVAndOTTPage(BasePage):
    def __init__(self, driver):
        self.driver = driver

    def close_onboarding_window(self):
        self.wait_for_element(IPTVAndOTTPageLocators.skip_onboarding_button)
        self.move_up()
        self.select()
        return True

    def open_profile(self):
        self.wait_for_element(IPTVAndOTTPageLocators.main_page_button)
        self.move_down(5)
        self.select()
        return True

    def get_iptv_login_text(self):
        return self.get_text_from_element(IPTVAndOTTPageLocators.check_iptv_login)
