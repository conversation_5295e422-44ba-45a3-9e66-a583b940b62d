import subprocess
import re
from test_data import creds


def get_player_state(host=creds.thomson_ip, port=5555):
    """Возвращает состояние плеера: playing, paused, stopped, none или None при ошибке"""
    try:
        out = subprocess.run(
            ["adb", "-s", f"{host}:{port}", "shell", "dumpsys", "media_session"],
            capture_output=True,
            text=True,
            timeout=10,
        ).stdout

        active = False
        for line in out.split("\n"):
            if "active=true" in line:
                active = True
            if active and "state=PlaybackState" in line:
                if "state=3" in line:
                    return "playing"
                elif "state=2" in line:
                    return "paused"
                elif "state=1" in line:
                    return "stopped"
                elif "state=0" in line:
                    return "none"
        return None
    except:
        return None


def get_player_position(host="*************", port=5555):
    """Возвращает позицию в секундах или None при ошибке"""
    try:
        out = subprocess.run(
            ["adb", "-s", f"{host}:{port}", "shell", "dumpsys", "media_session"],
            capture_output=True,
            text=True,
            timeout=10,
        ).stdout

        active = False
        for line in out.split("\n"):
            if "active=true" in line:
                active = True
            if active and "state=PlaybackState" in line:
                m = re.search(r"position=(\d+)", line)
                if m:
                    return int(m.group(1)) / 1000
        return None
    except:
        return None
