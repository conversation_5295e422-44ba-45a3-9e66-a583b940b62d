import os
import logging
import json
from typing import Optional, Dict, Any
from androidtv.adb_manager.adb_manager_sync import ADBPythonSync
from androidtv.basetv.basetv import BaseTV

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_player_state(
    host: str, port: int = 5555, adbkey: str = ""
) -> Optional[Dict[str, Any]]:
    """
    Получает полное состояние плеера и устройства Android TV.

    Args:
        host: IP адрес устройства
        port: Порт ADB (по умолчанию 5555)
        adbkey: Ключ ADB для аутентификации

    Returns:
        Словарь с информацией о состоянии плеера или None при ошибке
    """
    adb = None
    try:
        # Создаем ADB подключение
        adb = ADBPythonSync(host=host, port=port, adbkey=adbkey)

        # Проверяем доступность устройства
        if not adb.available:
            logger.error(f"Устройство {host}:{port} недоступно")
            return None

        # Создаем объект устройства
        device = BaseTV(adb=adb, host=host, port=port)

        # Обновляем состояние устройства
        device.update()

        # Собираем всю информацию о состоянии
        player_info = {
            # Основная информация об устройстве
            "device": {
                "current_app": device.current_app,
                "state": device.state,
                "is_on": device.is_on,
                "is_idle": device.is_idle,
                "running_apps": device.running_apps,
            },
            # Состояние медиа плеера
            "media": {
                "state": device.media_state,
                "title": device.media_title,
                "duration": device.media_duration,
                "position": device.media_position,
                "position_updated_at": device.media_position_updated_at,
            },
            # Аудио настройки
            "audio": {
                "volume_level": device.volume_level,
                "is_muted": device.is_volume_muted,
                "audio_state": device.audio_state,
            },
        }

        return player_info

    except Exception as e:
        logger.error(f"Ошибка при получении состояния плеера: {e}")
        return None

    finally:
        # Закрываем соединение
        if adb:
            try:
                adb.close()
            except Exception as e:
                logger.warning(f"Ошибка при закрытии ADB соединения: {e}")


def print_player_state(player_info: Dict[str, Any]) -> None:
    """
    Красиво выводит информацию о состоянии плеера.

    Args:
        player_info: Словарь с информацией о плеере
    """
    print("=" * 50)
    print("🔥 СОСТОЯНИЕ ANDROID TV ПЛЕЕРА")
    print("=" * 50)

    # Информация об устройстве
    device_info = player_info.get("device", {})
    print(f"📱 Устройство:")
    print(f"   Текущее приложение: {device_info.get('current_app', 'Неизвестно')}")
    print(f"   Состояние: {device_info.get('state', 'Неизвестно')}")
    print(f"   Включено: {'✅ Да' if device_info.get('is_on') else '❌ Нет'}")
    print(
        f"   В режиме ожидания: {'✅ Да' if device_info.get('is_idle') else '❌ Нет'}"
    )

    # Информация о медиа
    media_info = player_info.get("media", {})
    print(f"\n🎬 Медиа плеер:")
    print(f"   Состояние воспроизведения: {media_info.get('state', 'Неизвестно')}")
    print(f"   Название: {media_info.get('title', 'Нет данных')}")

    duration = media_info.get("duration")
    position = media_info.get("position")
    if duration and position:
        progress = (position / duration) * 100 if duration > 0 else 0
        print(f"   Прогресс: {position:.0f}с / {duration:.0f}с ({progress:.1f}%)")
    else:
        print(f"   Длительность: {duration or 'Неизвестно'}")
        print(f"   Позиция: {position or 'Неизвестно'}")

    # Информация об аудио
    audio_info = player_info.get("audio", {})
    print(f"\n🔊 Аудио:")
    volume = audio_info.get("volume_level")
    if volume is not None:
        print(f"   Громкость: {int(volume * 100)}%")
    else:
        print(f"   Громкость: Неизвестно")
    print(f"   Заглушен: {'🔇 Да' if audio_info.get('is_muted') else '🔊 Нет'}")
    print(f"   Аудио состояние: {audio_info.get('audio_state', 'Неизвестно')}")

    # Запущенные приложения
    running_apps = device_info.get("running_apps", [])
    if running_apps:
        print(f"\n📋 Запущенные приложения:")
        for app in running_apps:
            print(f"   • {app}")

    print("=" * 50)


if __name__ == "__main__":
    # Получаем конфигурацию из переменных окружения
    host = os.getenv("ANDROID_TV_HOST", "*************")
    port = int(os.getenv("ANDROID_TV_PORT", "5555"))

    print(f"🔍 Подключение к Android TV: {host}:{port}")

    # Получаем состояние плеера
    player_state = get_player_state(host, port)

    if player_state:
        # Красивый вывод
        print_player_state(player_state)

        # Также выводим JSON для отладки
        print("\n📋 JSON данные:")
        print(json.dumps(player_state, indent=2, ensure_ascii=False, default=str))
    else:
        print("❌ Не удалось получить состояние плеера")
