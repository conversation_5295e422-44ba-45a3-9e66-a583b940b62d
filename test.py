import os
import logging
from typing import Optional
from androidtv.adb_manager.adb_manager_sync import ADBPythonSync
from androidtv.basetv.basetv import BaseTV

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_device_state(host: str, port: int = 5555, adbkey: str = "") -> Optional[str]:
    """
    Получает состояние текущего приложения на Android TV устройстве.

    Args:
        host: IP адрес устройства
        port: Порт ADB (по умолчанию 5555)
        adbkey: Ключ ADB для аутентификации

    Returns:
        Название текущего приложения или None при ошибке
    """
    adb = None
    try:
        # Создаем ADB подключение
        adb = ADBPythonSync(host=host, port=port, adbkey=adbkey)

        # Проверяем доступность устройства
        if not adb.available:
            logger.error(f"Устройство {host}:{port} недоступно")
            return None

        # Создаем объект устройства
        device = BaseTV(adb=adb, host=host, port=port)

        # Получаем текущее приложение
        current_app = device._current_app
        logger.info(f"Текущее приложение: {current_app}")

        return current_app

    except Exception as e:
        logger.error(f"Ошибка при получении состояния устройства: {e}")
        return None

    finally:
        # Закрываем соединение
        if adb:
            try:
                adb.close()
            except Exception as e:
                logger.warning(f"Ошибка при закрытии ADB соединения: {e}")


if __name__ == "__main__":
    # Получаем конфигурацию из переменных окружения
    host = os.getenv("ANDROID_TV_HOST", "*************")
    port = int(os.getenv("ANDROID_TV_PORT", "5555"))

    state = get_device_state(host, port)
    if state:
        print(f"Устройство работает, текущее приложение: {state}")
    else:
        print("Не удалось получить состояние устройства")
