import subprocess
from typing import Optional, Dict, Any


def get_player_state(host: str, port: int = 5555) -> Optional[Dict[str, Any]]:
    """
    Получает состояние медиа плеера через ADB команды.

    Args:
        host: IP адрес устройства
        port: Порт ADB (по умолчанию 5555)

    Returns:
        Словарь с информацией о медиа плеере или None при ошибке
    """
    device_address = f"{host}:{port}"

    try:
        # Проверяем подключение к устройству
        result = subprocess.run(
            ["adb", "devices"], capture_output=True, text=True, timeout=10
        )

        if device_address not in result.stdout:
            print(f"❌ Устройство {device_address} не найдено")
            return None

        # Получаем информацию о медиа сессии
        media_session_result = subprocess.run(
            ["adb", "-s", device_address, "shell", "dumpsys", "media_session"],
            capture_output=True,
            text=True,
            timeout=10,
        )

        # Парсим результаты
        media_info = parse_media_session(media_session_result.stdout)
        audio_info = parse_audio_info(
            media_session_result.stdout
        )  # Используем те же данные

        return {"media": media_info, "audio": audio_info}

    except subprocess.TimeoutExpired:
        print("❌ Таймаут при выполнении команд")
        return None
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return None


def parse_media_session(media_output: str) -> Dict[str, Any]:
    """Парсит информацию о медиа сессии"""
    media_info = {
        "state": "unknown",
        "position": None,
    }

    try:
        lines = media_output.split("\n")
        in_active_session = False

        for line in lines:
            line = line.strip()

            # Ищем активную медиа сессию
            if "active=true" in line:
                in_active_session = True
                continue

            # Если мы в активной сессии, парсим данные
            if in_active_session:
                # Парсим состояние воспроизведения
                if "state=PlaybackState" in line:
                    if "state=3" in line:
                        media_info["state"] = "playing"
                    elif "state=2" in line:
                        media_info["state"] = "paused"
                    elif "state=1" in line:
                        media_info["state"] = "stopped"
                    elif "state=0" in line:
                        media_info["state"] = "none"

                    # Парсим позицию
                    if "position=" in line:
                        try:
                            import re

                            position_match = re.search(r"position=(\d+)", line)
                            if position_match:
                                position_ms = int(position_match.group(1))
                                media_info["position"] = position_ms / 1000
                        except:
                            pass

                # Выходим если дошли до конца сессии
                if line.startswith("Audio playback"):
                    break

    except:
        pass

    return media_info


def parse_audio_info(media_output: str) -> Dict[str, Any]:
    """Парсит информацию об аудио из media_session"""
    audio_info = {
        "has_audio": False,
        "is_muted": False,
    }

    try:
        # Проверяем наличие активного аудио воспроизведения
        if "Audio playback" in media_output and "uid=" in media_output:
            audio_info["has_audio"] = True

        # Также проверяем наличие audioAttrs
        if "audioAttrs=AudioAttributes" in media_output:
            audio_info["has_audio"] = True

        # Проверяем состояние mute
        for line in media_output.split("\n"):
            if "muted" in line.lower() or "mute" in line.lower():
                audio_info["is_muted"] = "true" in line.lower()
                break

    except:
        pass

    return audio_info


def format_time(seconds: float) -> str:
    """Форматирует время в секундах в читаемый формат"""
    if seconds is None:
        return "Неизвестно"

    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)

    if hours > 0:
        return f"{hours}ч {minutes}м {secs}с"
    elif minutes > 0:
        return f"{minutes}м {secs}с"
    else:
        return f"{secs}с"


def get_state_emoji(state: str) -> str:
    """Возвращает эмодзи для состояния плеера"""
    state_emojis = {
        "playing": "▶️",
        "paused": "⏸️",
        "stopped": "⏹️",
        "none": "⏹️",
        "unknown": "❓",
    }
    return state_emojis.get(state, "❓")


def print_player_state(player_info: Dict[str, Any]) -> None:
    """Выводит информацию о состоянии медиа плеера"""

    media_info = player_info.get("media", {})
    audio_info = player_info.get("audio", {})

    state = media_info.get("state", "unknown")
    position = media_info.get("position")
    has_audio = audio_info.get("has_audio", False)
    is_muted = audio_info.get("is_muted", False)

    print(f"Состояние: {get_state_emoji(state)} {state}")

    if position is not None:
        print(f"Позиция: {format_time(position)}")
    else:
        print(f"Позиция: Неизвестно")

    print(f"Аудио: {'🔊' if has_audio else '🔇'} {'Есть' if has_audio else 'Нет'}")
    if has_audio:
        print(f"Заглушен: {'Да' if is_muted else 'Нет'}")


if __name__ == "__main__":
    host = "*************"
    port = 5555

    player_state = get_player_state(host, port)

    if player_state:
        print_player_state(player_state)
    else:
        print("❌ Не удалось получить состояние плеера")
