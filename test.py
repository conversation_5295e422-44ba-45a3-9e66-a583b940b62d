import os
import logging
import json
import subprocess
from typing import Optional, Dict, Any
from androidtv.adb_manager.adb_manager_sync import ADBPythonSync
from androidtv.basetv.basetv import BaseTV

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_player_state_adb(host: str, port: int = 5555) -> Optional[Dict[str, Any]]:
    """
    Получает состояние плеера через прямые ADB команды.

    Args:
        host: IP адрес устройства
        port: Порт ADB (по умолчанию 5555)

    Returns:
        Словарь с информацией о состоянии плеера или None при ошибке
    """
    device_address = f"{host}:{port}"

    try:
        # Проверяем подключение к устройству
        result = subprocess.run(
            ["adb", "devices"], capture_output=True, text=True, timeout=10
        )

        if device_address not in result.stdout:
            logger.error(
                f"Устройство {device_address} не найдено в списке ADB устройств"
            )
            return None

        # Получаем текущее приложение - используем два отдельных процесса
        dumpsys_result = subprocess.run(
            ["adb", "-s", device_address, "shell", "dumpsys", "window", "windows"],
            capture_output=True,
            text=True,
            timeout=10,
        )

        # Фильтруем результат локально
        current_app_output = ""
        if dumpsys_result.returncode == 0:
            for line in dumpsys_result.stdout.split("\n"):
                if "mCurrentFocus" in line:
                    current_app_output = line
                    break

        # Создаем объект результата для совместимости
        class MockResult:
            def __init__(self, stdout, returncode):
                self.stdout = stdout
                self.returncode = returncode

        current_app_result = MockResult(current_app_output, dumpsys_result.returncode)

        # Получаем информацию о медиа сессии
        media_session_result = subprocess.run(
            ["adb", "-s", device_address, "shell", "dumpsys", "media_session"],
            capture_output=True,
            text=True,
            timeout=10,
        )

        # Получаем информацию об аудио
        audio_result = subprocess.run(
            ["adb", "-s", device_address, "shell", "dumpsys", "audio"],
            capture_output=True,
            text=True,
            timeout=10,
        )

        # Получаем список запущенных приложений
        running_apps_result = subprocess.run(
            ["adb", "-s", device_address, "shell", "pm", "list", "packages", "-3"],
            capture_output=True,
            text=True,
            timeout=10,
        )

        # Парсим результаты
        current_app = parse_current_app(current_app_result.stdout)

        player_info = {
            "device": {
                "current_app": current_app,
                "state": "on" if current_app_result.returncode == 0 else "unknown",
                "is_on": True,
                "is_idle": False,
                "running_apps": parse_running_apps(running_apps_result.stdout),
            },
            "media": parse_media_session(media_session_result.stdout),
            "audio": parse_audio_info(audio_result.stdout),
        }

        return player_info

    except subprocess.TimeoutExpired:
        logger.error("Таймаут при выполнении ADB команд")
        return None
    except Exception as e:
        logger.error(f"Ошибка при получении состояния через ADB: {e}")
        return None


def parse_current_app(dumpsys_output: str) -> str:
    """Парсит текущее приложение из вывода dumpsys"""
    try:
        for line in dumpsys_output.split("\n"):
            if "mCurrentFocus" in line:
                # Извлекаем имя пакета из строки типа: mCurrentFocus=Window{... com.example.app/...}
                if "{" in line and "}" in line:
                    window_info = line.split("{")[1].split("}")[0]
                    # Ищем пакет в формате com.example.app/activity
                    parts = window_info.split()
                    for part in parts:
                        if "/" in part and "." in part:
                            package_name = part.split("/")[0]
                            return package_name
        return "Неизвестно"
    except Exception as e:
        logger.warning(f"Ошибка парсинга текущего приложения: {e}")
        return "Ошибка парсинга"


def parse_media_session(media_output: str) -> Dict[str, Any]:
    """Парсит информацию о медиа сессии"""
    media_info = {
        "state": "unknown",
        "title": None,
        "duration": None,
        "position": None,
        "position_updated_at": None,
    }

    try:
        lines = media_output.split("\n")
        in_active_session = False

        for line in lines:
            line = line.strip()

            # Ищем активную медиа сессию
            if "active=true" in line:
                in_active_session = True
                continue

            # Если мы в активной сессии, парсим данные
            if in_active_session:
                # Парсим состояние воспроизведения из строки state=PlaybackState {...}
                if "state=PlaybackState" in line:
                    # Извлекаем состояние из строки типа: state=PlaybackState {state=3, position=14421275, ...}
                    if "state=3" in line:
                        media_info["state"] = "playing"
                    elif "state=2" in line:
                        media_info["state"] = "paused"
                    elif "state=1" in line:
                        media_info["state"] = "stopped"
                    elif "state=0" in line:
                        media_info["state"] = "none"

                    # Парсим позицию
                    if "position=" in line:
                        try:
                            # Ищем position=число
                            import re

                            position_match = re.search(r"position=(\d+)", line)
                            if position_match:
                                position_ms = int(position_match.group(1))
                                media_info["position"] = (
                                    position_ms / 1000
                                )  # конвертируем в секунды
                        except Exception as e:
                            logger.debug(f"Ошибка парсинга позиции: {e}")

                # Парсим метаданные из строки metadata:size=9, description=...
                if "metadata:" in line and "description=" in line:
                    try:
                        # Извлекаем описание после description=
                        desc_start = line.find("description=") + len("description=")
                        description = line[desc_start:].split(",")[0].strip()
                        if description and description != "null":
                            media_info["title"] = description
                    except Exception as e:
                        logger.debug(f"Ошибка парсинга метаданных: {e}")

                # Если дошли до следующей сессии или конца, выходим
                if line.startswith("Audio playback") or "Sessions Stack" in line:
                    break

    except Exception as e:
        logger.warning(f"Ошибка при парсинге медиа сессии: {e}")

    return media_info


def parse_audio_info(audio_output: str) -> Dict[str, Any]:
    """Парсит информацию об аудио"""
    audio_info = {
        "volume_level": None,
        "is_muted": False,
        "audio_state": "unknown",
    }

    try:
        lines = audio_output.split("\n")

        for line in lines:
            line = line.strip()

            # Ищем информацию о громкости
            if "Stream volumes" in line or "volume" in line.lower():
                # Попытка найти уровень громкости
                if "STREAM_MUSIC" in line:
                    try:
                        # Парсим строку типа: STREAM_MUSIC: 7 (speaker): 7, 15 (headset): 15
                        parts = line.split(":")
                        if len(parts) > 1:
                            volume_part = parts[1].strip()
                            volume_num = volume_part.split()[0]
                            audio_info["volume_level"] = (
                                int(volume_num) / 15.0
                            )  # нормализуем к 0-1
                    except:
                        pass

            # Проверяем состояние mute
            if "muted" in line.lower() or "mute" in line.lower():
                audio_info["is_muted"] = "true" in line.lower()

    except Exception as e:
        logger.warning(f"Ошибка при парсинге аудио информации: {e}")

    return audio_info


def parse_running_apps(packages_output: str) -> list:
    """Парсит список запущенных приложений"""
    try:
        apps = []
        for line in packages_output.split("\n"):
            if line.startswith("package:"):
                app_name = line.replace("package:", "").strip()
                apps.append(app_name)
        return apps[:10]  # ограничиваем до 10 приложений
    except Exception:
        return []


def get_player_state(
    host: str, port: int = 5555, adbkey: str = ""
) -> Optional[Dict[str, Any]]:
    """
    Получает полное состояние плеера и устройства Android TV.

    Args:
        host: IP адрес устройства
        port: Порт ADB (по умолчанию 5555)
        adbkey: Ключ ADB для аутентификации

    Returns:
        Словарь с информацией о состоянии плеера или None при ошибке
    """
    adb = None
    try:
        # Создаем ADB подключение
        adb = ADBPythonSync(host=host, port=port, adbkey=adbkey)

        # Проверяем доступность устройства
        if not adb.available:
            logger.error(f"Устройство {host}:{port} недоступно")
            return None

        # Создаем объект устройства
        device = BaseTV(adb=adb, host=host, port=port)

        # Обновляем состояние устройства
        device.update()

        # Собираем всю информацию о состоянии
        player_info = {
            # Основная информация об устройстве
            "device": {
                "current_app": device.current_app,
                "state": device.state,
                "is_on": device.is_on,
                "is_idle": device.is_idle,
                "running_apps": device.running_apps,
            },
            # Состояние медиа плеера
            "media": {
                "state": device.media_state,
                "title": device.media_title,
                "duration": device.media_duration,
                "position": device.media_position,
                "position_updated_at": device.media_position_updated_at,
            },
            # Аудио настройки
            "audio": {
                "volume_level": device.volume_level,
                "is_muted": device.is_volume_muted,
                "audio_state": device.audio_state,
            },
        }

        return player_info

    except Exception as e:
        logger.error(f"Ошибка при получении состояния плеера: {e}")
        return None

    finally:
        # Закрываем соединение
        if adb:
            try:
                adb.close()
            except Exception as e:
                logger.warning(f"Ошибка при закрытии ADB соединения: {e}")


def format_time(seconds: float) -> str:
    """Форматирует время в секундах в читаемый формат"""
    if seconds is None:
        return "Неизвестно"

    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)

    if hours > 0:
        return f"{hours}ч {minutes}м {secs}с"
    elif minutes > 0:
        return f"{minutes}м {secs}с"
    else:
        return f"{secs}с"


def get_app_name(package_name: str) -> str:
    """Возвращает читаемое имя приложения"""
    app_names = {
        "com.spbtv.velcom": "📺 Voka (A1)",
        "ru.kinopoisk.tv": "🎬 Кинопоиск",
        "com.teamsmart.videomanager.tv": "📹 Video Manager",
        "by.video.videobel": "📺 VideoBel",
        "com.alphainventor.filemanager": "📁 File Manager",
        "com.example.phoneproject": "📱 Phone Project",
        "io.appium.settings": "⚙️ Appium Settings",
        "io.appium.uiautomator2.server": "🤖 UI Automator",
        "com.yablio.sendfilestotv": "📤 Send Files to TV",
    }
    return app_names.get(package_name, package_name)


def get_state_emoji(state: str) -> str:
    """Возвращает эмодзи для состояния плеера"""
    state_emojis = {
        "playing": "▶️ Воспроизведение",
        "paused": "⏸️ Пауза",
        "stopped": "⏹️ Остановлено",
        "none": "⏹️ Нет медиа",
        "unknown": "❓ Неизвестно",
    }
    return state_emojis.get(state, f"❓ {state}")


def print_player_state(player_info: Dict[str, Any]) -> None:
    """
    Красиво выводит информацию о состоянии плеера.

    Args:
        player_info: Словарь с информацией о плеере
    """
    print("=" * 60)
    print("🔥 СОСТОЯНИЕ ANDROID TV ПЛЕЕРА")
    print("=" * 60)

    # Информация об устройстве
    device_info = player_info.get("device", {})
    current_app = device_info.get("current_app", "Неизвестно")
    print(f"📱 Устройство:")
    print(f"   Активное приложение: {get_app_name(current_app)}")
    print(f"   Состояние: {device_info.get('state', 'Неизвестно')}")
    print(f"   Включено: {'✅ Да' if device_info.get('is_on') else '❌ Нет'}")
    print(
        f"   В режиме ожидания: {'✅ Да' if device_info.get('is_idle') else '❌ Нет'}"
    )

    # Информация о медиа
    media_info = player_info.get("media", {})
    print(f"\n🎬 Медиа плеер:")
    state = media_info.get("state", "unknown")
    print(f"   Состояние: {get_state_emoji(state)}")

    title = media_info.get("title")
    if title:
        print(f"   📺 Контент: {title}")
    else:
        print(f"   📺 Контент: Нет данных")

    position = media_info.get("position")
    duration = media_info.get("duration")

    if position is not None:
        print(f"   ⏱️ Позиция: {format_time(position)}")
        if duration:
            progress = (position / duration) * 100 if duration > 0 else 0
            print(
                f"   📊 Прогресс: {progress:.1f}% ({format_time(position)} / {format_time(duration)})"
            )
    else:
        print(f"   ⏱️ Позиция: Неизвестно")

    # Информация об аудио
    audio_info = player_info.get("audio", {})
    print(f"\n🔊 Аудио:")
    volume = audio_info.get("volume_level")
    if volume is not None:
        volume_percent = int(volume * 100)
        volume_bar = "█" * (volume_percent // 10) + "░" * (10 - volume_percent // 10)
        print(f"   🔊 Громкость: {volume_percent}% [{volume_bar}]")
    else:
        print(f"   🔊 Громкость: Неизвестно")
    print(f"   🔇 Заглушен: {'Да' if audio_info.get('is_muted') else 'Нет'}")

    # Запущенные приложения (показываем только основные)
    running_apps = device_info.get("running_apps", [])
    if running_apps:
        print(f"\n📋 Установленные приложения:")
        main_apps = [app for app in running_apps if not app.startswith("io.appium")]
        for app in main_apps[:5]:  # показываем только первые 5
            print(f"   • {get_app_name(app)}")
        if len(main_apps) > 5:
            print(f"   ... и еще {len(main_apps) - 5} приложений")

    print("=" * 60)


if __name__ == "__main__":
    # Получаем конфигурацию из переменных окружения
    host = os.getenv("ANDROID_TV_HOST", "*************")
    port = int(os.getenv("ANDROID_TV_PORT", "5555"))

    print(f"🔍 Подключение к Android TV: {host}:{port}")

    # Сначала пробуем через ADB команды
    print("📱 Попытка получить состояние через ADB команды...")
    player_state = get_player_state_adb(host, port)

    # Если не получилось, пробуем через androidtv библиотеку
    if not player_state:
        print("📚 Попытка получить состояние через androidtv библиотеку...")
        player_state = get_player_state(host, port)

    if player_state:
        # Красивый вывод
        print_player_state(player_state)

        # Также выводим JSON для отладки
        print("\n📋 JSON данные:")
        print(json.dumps(player_state, indent=2, ensure_ascii=False, default=str))
    else:
        print("❌ Не удалось получить состояние плеера ни одним из способов")
        print("💡 Убедитесь, что:")
        print("   • Устройство подключено к ADB (adb devices)")
        print("   • IP адрес и порт указаны правильно")
        print("   • На устройстве включена отладка по USB")
