import subprocess
import re


def get_time(s):
    if not s:
        return "Неизвестно"
    h, m, sec = int(s // 3600), int((s % 3600) // 60), int(s % 60)
    return f"{h}ч {m}м {sec}с" if h else f"{m}м {sec}с" if m else f"{sec}с"


try:
    out = subprocess.run(
        ["adb", "-s", "192.168.0.134:5555", "shell", "dumpsys", "media_session"],
        capture_output=True,
        text=True,
        timeout=10,
    ).stdout

    state = pos = audio = "unknown"
    active = False

    for line in out.split("\n"):
        if "active=true" in line:
            active = True
        if active and "state=PlaybackState" in line:
            if "state=3" in line:
                state = "▶️ playing"
            elif "state=2" in line:
                state = "⏸️ paused"
            elif "state=1" in line:
                state = "⏹️ stopped"
            m = re.search(r"position=(\d+)", line)
            if m:
                pos = get_time(int(m.group(1)) / 1000)
        if "Audio playback" in out and "uid=" in out:
            audio = "🔊 Есть"
        elif "audioAttrs=AudioAttributes" in out:
            audio = "🔊 Есть"

    print(f"Состояние: {state}")
    print(f"Позиция: {pos}")
    print(f"Аудио: {audio}")

except:
    print("❌ Ошибка")
