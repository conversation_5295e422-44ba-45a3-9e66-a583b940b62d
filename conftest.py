import pytest
import subprocess
import time
import os
import signal
import requests
from appium import webdriver
from appium.options.android import UiAutomator2Options
from test_data import creds


@pytest.fixture(scope="session")
def adb_connect():
    """Фикстура для подключения к adb"""
    subprocess.run(["adb", "connect", creds.thomson_ip])
    yield
    subprocess.run(["adb", "disconnect", creds.thomson_ip])


@pytest.fixture(scope="session")
def appium_server(adb_connect):
    """Фикстура для автоматического запуска и остановки Appium сервера"""
    appium_url = "http://127.0.0.1:4723"

    # Проверяем, не запущен ли уже сервер
    try:
        response = requests.get(f"{appium_url}/status", timeout=5)
        if response.status_code == 200:
            print("Appium сервер уже запущен")
            yield appium_url
            return
    except requests.exceptions.RequestException:
        pass

    # Запускаем Appium сервер
    print("Запуск Appium сервера...")
    process = subprocess.Popen(
        ["appium", "--address", "127.0.0.1", "--port", "4723"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid,  # Создаем новую группу процессов
    )

    # Ждем запуска сервера
    max_wait = 30  # максимум 30 секунд
    wait_time = 0
    server_ready = False

    while wait_time < max_wait:
        try:
            response = requests.get(f"{appium_url}/status", timeout=2)
            if response.status_code == 200:
                server_ready = True
                print("Appium сервер успешно запущен")
                break
        except requests.exceptions.RequestException:
            pass

        time.sleep(1)
        wait_time += 1

    if not server_ready:
        # Убиваем процесс если не удалось запустить
        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
        raise Exception("Не удалось запустить Appium сервер в течение 30 секунд")

    try:
        yield appium_url
    finally:
        # Останавливаем сервер после всех тестов
        print("Остановка Appium сервера...")
        try:
            os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            process.wait(timeout=10)
        except (ProcessLookupError, subprocess.TimeoutExpired):
            # Принудительно убиваем если не остановился
            try:
                os.killpg(os.getpgid(process.pid), signal.SIGKILL)
            except ProcessLookupError:
                pass
        print("Appium сервер остановлен")


@pytest.fixture(scope="session")
def appium_options():
    """Фикстура для настройки опций Appium"""
    options = UiAutomator2Options()

    # Путь к APK файлу
    apk_path = os.path.join(os.path.dirname(__file__), "test_data", "voka.apk")
    if not os.path.exists(apk_path):
        raise FileNotFoundError(f"APK файл не найден: {apk_path}")

    # Базовые capabilities
    options.set_capability("platformName", "Android")
    options.set_capability("deviceName", "R1")
    options.set_capability("platformVersion", "9")
    options.set_capability("automationName", "UiAutomator2")
    options.set_capability("app", os.path.abspath(apk_path))
    options.set_capability("newCommandTimeout", 300)
    options.set_capability("autoGrantPermissions", True)

    return options


@pytest.fixture(scope="function")
def driver(appium_server, appium_options):
    """Основная фикстура для инициализации драйвера Appium"""
    print(f"Подключение к Appium серверу: {appium_server}")

    # Инициализация драйвера
    driver = webdriver.Remote(appium_server, options=appium_options)

    # Неявное ожидание
    driver.implicitly_wait(50)

    yield driver
    # Удаление приложения после завершения тестов
    # driver.remove_app("com.spbtv.velcom")
    # Закрытие драйвера после теста
    driver.quit()
